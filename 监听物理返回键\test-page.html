<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>测试页面</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <style>
      body {
        font-family: Arial, sans-serif;
        padding: 20px;
        background-color: #f0f0f0;
      }
      .container {
        max-width: 600px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .btn {
        display: inline-block;
        padding: 12px 24px;
        margin: 10px 5px;
        background-color: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        border: none;
        cursor: pointer;
        font-size: 16px;
      }
      .btn:hover {
        background-color: #0056b3;
      }
      .btn-success {
        background-color: #28a745;
      }
      .btn-success:hover {
        background-color: #1e7e34;
      }
      .btn-warning {
        background-color: #ffc107;
        color: #212529;
      }
      .btn-warning:hover {
        background-color: #e0a800;
      }
      .device-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        border-left: 4px solid #007bff;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>返回键拦截测试页面</h1>

      <div class="device-info">
        <h3>当前设备信息：</h3>
        <p>
          <strong>User Agent:</strong>
          <span id="userAgent"></span>
        </p>
        <p>
          <strong>是否为 iOS:</strong>
          <span id="isIOS"></span>
        </p>
        <p>
          <strong>是否为 Safari:</strong>
          <span id="isSafari"></span>
        </p>
        <p>
          <strong>是否为 iOS Safari:</strong>
          <span id="isIOSSafari"></span>
        </p>
      </div>

      <h3>测试不同的拦截方案：</h3>

      <div style="margin: 20px 0">
        <a href="back2.html" class="btn">原始方案测试</a>
        <p style="margin: 5px 0; color: #666; font-size: 14px">使用原始的 history.pushState 方案</p>
      </div>

      <div style="margin: 20px 0">
        <a href="back2-ios-safari-enhanced.html" class="btn btn-success">iOS Safari 增强版</a>
        <p style="margin: 5px 0; color: #666; font-size: 14px">
          专门针对 iOS Safari 优化的多重拦截方案
        </p>
      </div>

      <div style="margin: 20px 0">
        <a
          href="back2-ios-safari-ultimate.html"
          class="btn"
          style="background-color: #6f42c1; color: white"
        >
          🛡️ iOS Safari 终极版
        </a>
        <p style="margin: 5px 0; color: #666; font-size: 14px">
          <strong>推荐！</strong>
          最新的iOS Safari专用拦截方案，包含实时日志和状态监控
        </p>
      </div>

      <div style="margin: 20px 0">
        <a href="back2-iosChrom.html" class="btn btn-warning">iOS Chrome 版本</a>
        <p style="margin: 5px 0; color: #666; font-size: 14px">使用确认对话框的方案</p>
      </div>

      <div style="margin: 20px 0">
        <a href="back27.29.html" class="btn">最新综合版本</a>
        <p style="margin: 5px 0; color: #666; font-size: 14px">包含用户激活检测的综合方案</p>
      </div>

      <h3>测试说明：</h3>
      <ol>
        <li>点击上面的链接进入测试页面</li>
        <li>在测试页面中尝试点击浏览器的返回按钮</li>
        <li>观察是否能够成功拦截返回操作</li>
        <li>如果拦截失败，请尝试其他方案</li>
      </ol>

      <h3>iOS Safari 特殊说明：</h3>
      <ul>
        <li>iOS Safari 对 history API 有特殊限制</li>
        <li>需要用户交互后才能使用某些 API</li>
        <li>推荐使用 "iOS Safari 增强版" 进行测试</li>
        <li>该版本使用了多重拦截策略，包括 hash 拦截、事件拦截等</li>
      </ul>

      <div style="margin-top: 30px; padding: 15px; background-color: #fff3cd; border-radius: 5px">
        <strong>注意：</strong>
        拦截浏览器返回按钮可能影响用户体验，请谨慎使用。建议只在必要的场景下（如考试系统、重要表单等）使用此功能。
      </div>
    </div>

    <script>
      // 显示设备信息
      function displayDeviceInfo() {
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
        const isIOSSafari = isIOS && isSafari

        document.getElementById("userAgent").textContent = navigator.userAgent
        document.getElementById("isIOS").textContent = isIOS ? "是" : "否"
        document.getElementById("isSafari").textContent = isSafari ? "是" : "否"
        document.getElementById("isIOSSafari").textContent = isIOSSafari ? "是" : "否"

        // 根据设备类型高亮推荐方案
        if (isIOSSafari) {
          const enhancedBtn = document.querySelector('a[href="back2-ios-safari-enhanced.html"]')
          if (enhancedBtn) {
            enhancedBtn.style.boxShadow = "0 0 10px #28a745"
            enhancedBtn.style.fontWeight = "bold"
          }
        }
      }

      // 页面加载完成后显示设备信息
      window.addEventListener("load", displayDeviceInfo)
      document.addEventListener("DOMContentLoaded", displayDeviceInfo)
    </script>
  </body>
</html>
