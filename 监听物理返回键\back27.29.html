<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
  </head>
  <body>
    <div id="app">页面2（彻底阻止返回）</div>

    <script>
      ;(function () {
        // 方案1: 预建历史栈 + setTimeout延迟执行
        function buildInitialStack() {
          // 建立深层历史栈
          for (let i = 0; i < 5; i++) {
            history.pushState({ step: i }, "", location.href)
          }
        }

        // 立即建立初始保护
        buildInitialStack()

        // 页面加载完成后再次建立保护
        if (document.readyState === "loading") {
          document.addEventListener("DOMContentLoaded", buildInitialStack)
        }
        window.addEventListener("load", buildInitialStack)
      })()
      const app = new Vue({
        el: "#app",
        mounted() {},
        methods: {}
      })
    </script>
  </body>
</html>
