<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2 - iOS Safari 终极版</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        min-height: 100vh;
      }
      .container {
        max-width: 600px;
        margin: 0 auto;
        background: rgba(255,255,255,0.1);
        padding: 30px;
        border-radius: 15px;
        backdrop-filter: blur(10px);
      }
      .status-panel {
        background: rgba(255,255,255,0.2);
        padding: 15px;
        border-radius: 10px;
        margin: 15px 0;
      }
      .btn {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      .btn:hover {
        background: rgba(255,255,255,0.3);
      }
      .log {
        background: rgba(0,0,0,0.3);
        padding: 10px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="container">
        <h1>🛡️ iOS Safari 终极拦截</h1>
        
        <div class="status-panel">
          <h3>📱 设备状态</h3>
          <p><strong>设备类型:</strong> {{ deviceInfo }}</p>
          <p><strong>拦截状态:</strong> <span :style="{ color: interceptActive ? '#4CAF50' : '#f44336' }">{{ interceptActive ? '✅ 已激活' : '❌ 未激活' }}</span></p>
          <p><strong>用户交互:</strong> <span :style="{ color: userActivated ? '#4CAF50' : '#FFC107' }">{{ userActivated ? '✅ 已检测' : '⏳ 等待中' }}</span></p>
          <p><strong>拦截次数:</strong> {{ interceptCount }}</p>
        </div>

        <div class="status-panel">
          <h3>🎮 测试控制</h3>
          <button class="btn" @click="testInterception">🧪 测试拦截效果</button>
          <button class="btn" @click="forceActivate">⚡ 强制激活</button>
          <button class="btn" @click="clearLog">🗑️ 清空日志</button>
        </div>

        <div class="status-panel">
          <h3>📋 拦截日志</h3>
          <div class="log" ref="logContainer">
            <div v-for="(log, index) in logs" :key="index" :style="{ color: getLogColor(log.type) }">
              [{{ log.time }}] {{ log.message }}
            </div>
          </div>
        </div>

        <div class="status-panel">
          <h3>ℹ️ 使用说明</h3>
          <p>1. 页面加载后会自动激活拦截机制</p>
          <p>2. 在页面上进行任意交互（点击、滚动等）以激活用户交互检测</p>
          <p>3. 尝试使用浏览器返回按钮测试拦截效果</p>
          <p>4. 观察拦截日志了解拦截过程</p>
        </div>
      </div>
    </div>

    <script>
      // iOS Safari 终极拦截方案
      ;(function () {
        // 全局状态
        window.BackInterceptor = {
          isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream,
          isSafari: /^((?!chrome|android).)*safari/i.test(navigator.userAgent),
          userActivated: false,
          interceptActive: false,
          interceptCount: 0,
          logs: [],
          hashChangeBlocked: false,
          originalHash: location.hash,
          
          // 日志记录
          log: function(message, type = 'info') {
            const time = new Date().toLocaleTimeString()
            this.logs.push({ time, message, type })
            console.log(`[${type.toUpperCase()}] ${message}`)
            
            // 通知Vue更新
            if (window.vueApp) {
              window.vueApp.logs = [...this.logs]
              window.vueApp.interceptCount = this.interceptCount
            }
          },

          // 用户激活
          activate: function() {
            if (!this.userActivated) {
              this.userActivated = true
              this.log('用户交互已激活', 'success')
              
              if (window.vueApp) {
                window.vueApp.userActivated = true
              }
              
              // 非iOS Safari使用history API增强
              if (!this.isIOSSafari()) {
                for (let i = 0; i < 50; i++) {
                  try {
                    history.pushState({ index: i, timestamp: Date.now() }, null, location.href)
                  } catch (e) {
                    break
                  }
                }
                this.log('History API 增强完成', 'info')
              }
            }
          },

          // 检测是否为iOS Safari
          isIOSSafari: function() {
            return this.isIOS && this.isSafari
          },

          // 拦截计数
          incrementIntercept: function() {
            this.interceptCount++
            this.log(`拦截次数: ${this.interceptCount}`, 'warning')
          },

          // iOS Safari 专用拦截
          setupIOSSafariIntercept: function() {
            this.log('启动 iOS Safari 专用拦截', 'info')
            
            // 策略1: Hash 拦截 - 最有效的iOS Safari方案
            if (!location.hash || location.hash === '#') {
              location.hash = 'no-back-ios-ultimate'
            }

            // 监听 hashchange - 核心拦截机制
            window.addEventListener('hashchange', (e) => {
              if (!this.hashChangeBlocked) {
                this.hashChangeBlocked = true
                this.incrementIntercept()
                this.log('Hash 变化被拦截', 'warning')
                
                // 立即恢复hash
                setTimeout(() => {
                  location.hash = 'no-back-ios-ultimate'
                  this.hashChangeBlocked = false
                }, 1)
                
                e.preventDefault()
                e.stopPropagation()
                return false
              }
            }, { capture: true, passive: false })

            // 策略2: pagehide 事件 - iOS特有
            window.addEventListener('pagehide', (e) => {
              this.log('pagehide 事件触发', 'warning')
              if (e.persisted) {
                e.preventDefault()
                return false
              }
            }, { capture: true })

            // 策略3: beforeunload 增强
            window.addEventListener('beforeunload', (e) => {
              this.log('beforeunload 事件触发', 'warning')
              e.preventDefault()
              e.returnValue = '确定要离开此页面吗？您的操作可能会丢失。'
              return '确定要离开此页面吗？您的操作可能会丢失。'
            })

            // 策略4: popstate 备用拦截
            window.addEventListener('popstate', (e) => {
              this.incrementIntercept()
              this.log('popstate 事件被拦截', 'warning')
              
              // 立即推送新状态
              setTimeout(() => {
                try {
                  history.pushState({ 
                    index: Date.now(), 
                    ios: true, 
                    ultimate: true 
                  }, null, location.href)
                } catch (err) {
                  this.log('pushState 失败: ' + err.message, 'error')
                }
              }, 1)
            }, { capture: true })

            // 策略5: 定期维护 - iOS Safari专用
            setInterval(() => {
              if (this.userActivated && location.hash !== 'no-back-ios-ultimate') {
                location.hash = 'no-back-ios-ultimate'
                this.log('Hash 状态维护', 'info')
              }
            }, 1000)
          },

          // 通用浏览器拦截
          setupGeneralIntercept: function() {
            this.log('启动通用浏览器拦截', 'info')
            
            // 建立深层历史栈
            for (let i = 0; i < 100; i++) {
              try {
                history.pushState({ index: i, timestamp: Date.now() }, null, location.href)
              } catch (e) {
                break
              }
            }

            // popstate 拦截
            window.addEventListener('popstate', (e) => {
              this.incrementIntercept()
              this.log('popstate 事件被拦截', 'warning')
              
              if (this.userActivated) {
                history.pushState({ index: Date.now() }, null, location.href)
              } else {
                setTimeout(() => {
                  try {
                    history.pushState({ index: Date.now() }, null, location.href)
                  } catch (err) {
                    this.log('pushState 失败: ' + err.message, 'error')
                  }
                }, 0)
              }
            })

            // 定期维护历史栈
            setInterval(() => {
              if (this.userActivated) {
                try {
                  history.pushState({ index: Date.now() }, null, location.href)
                } catch (e) {
                  // 静默失败
                }
              }
            }, 2000)
          },

          // 初始化
          init: function() {
            this.log('初始化返回键拦截器', 'info')
            this.log(`设备: iOS=${this.isIOS}, Safari=${this.isSafari}`, 'info')
            
            this.interceptActive = true
            
            // 根据设备选择策略
            if (this.isIOSSafari()) {
              this.setupIOSSafariIntercept()
            } else {
              this.setupGeneralIntercept()
            }

            // 用户交互监听
            const events = [
              'click', 'touchstart', 'touchend', 'touchmove',
              'mousedown', 'mouseup', 'mousemove', 'scroll',
              'keydown', 'keyup', 'focus', 'blur'
            ]
            
            events.forEach(event => {
              document.addEventListener(event, () => this.activate(), {
                once: false,
                passive: true,
                capture: true
              })
            })

            // Backspace 键拦截
            document.addEventListener('keydown', (e) => {
              if (e.key === 'Backspace' || e.keyCode === 8) {
                if (!['INPUT', 'TEXTAREA'].includes(e.target.tagName)) {
                  e.preventDefault()
                  e.stopPropagation()
                  this.activate()
                  this.log('Backspace 键被拦截', 'warning')
                  return false
                }
              }
            }, { capture: true })

            this.log('拦截器初始化完成', 'success')
          }
        }

        // 启动拦截器
        window.BackInterceptor.init()
      })()

      // Vue 应用
      const app = new Vue({
        el: "#app",
        data: {
          deviceInfo: '',
          interceptActive: false,
          userActivated: false,
          interceptCount: 0,
          logs: []
        },
        mounted() {
          window.vueApp = this
          this.updateDeviceInfo()
          this.interceptActive = window.BackInterceptor.interceptActive
          this.logs = window.BackInterceptor.logs
        },
        methods: {
          updateDeviceInfo() {
            const interceptor = window.BackInterceptor
            if (interceptor.isIOSSafari()) {
              this.deviceInfo = '📱 iOS Safari'
            } else if (interceptor.isIOS) {
              this.deviceInfo = '📱 iOS (非Safari)'
            } else if (interceptor.isSafari) {
              this.deviceInfo = '🖥️ Safari (非iOS)'
            } else {
              this.deviceInfo = '🖥️ 其他浏览器'
            }
          },
          testInterception() {
            alert('🧪 测试提示：\n\n1. 关闭此对话框\n2. 点击浏览器的返回按钮\n3. 观察是否被成功拦截\n4. 查看拦截日志了解详情')
          },
          forceActivate() {
            window.BackInterceptor.activate()
            this.userActivated = true
          },
          clearLog() {
            window.BackInterceptor.logs = []
            this.logs = []
            window.BackInterceptor.interceptCount = 0
            this.interceptCount = 0
          },
          getLogColor(type) {
            const colors = {
              'info': '#81C784',
              'success': '#4CAF50',
              'warning': '#FFB74D',
              'error': '#E57373'
            }
            return colors[type] || '#FFFFFF'
          }
        }
      })
    </script>
  </body>
</html>
