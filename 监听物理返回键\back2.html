<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
  </head>
  <body>
    <div id="app">页面2（彻底阻止返回）11</div>

    <script>
      const Fn = () => {
        console.log("Fn")
      }
      document.addEventListener(
        "click",
        () => {
          // 建立历史栈
          for (let i = 0; i < 20; i++) {
            history.pushState({ step: i }, "", location.href)
          }
        },
        { once: true }
      )
      window.addEventListener("popstate", Fn)
      // 简单的返回键拦截方案 + iOS Safari 兼容
      ;(function () {
        // 检测是否为 iOS Safari
        let isIOSSafari =
          /iPad|iPhone|iPod/.test(navigator.userAgent) &&
          /^((?!chrome|android).)*safari/i.test(navigator.userAgent) &&
          !window.MSStream

        // if (isIOSSafari) {
        // } else {
        //   // 建立历史栈
        //   for (let i = 0; i < 20; i++) {
        //     history.pushState({ step: i }, "", location.href)
        //   }
        //   // 监听 popstate 事件
        //   window.addEventListener("popstate", function () {
        //     history.pushState({ step: Date.now() }, "", location.href)
        //   })
        // }
      })()

      const app = new Vue({
        el: "#app",
        mounted() {},
        methods: {}
      })
    </script>
  </body>
</html>
