<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2 - iOS Safari 增强版</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
  </head>
  <body>
    <div id="app">
      <h3>页面2（iOS Safari 增强拦截）</h3>
      <p>设备信息：<span id="deviceInfo"></span></p>
      <p>拦截状态：<span id="interceptStatus"></span></p>
      <button @click="testInterception">测试拦截效果</button>
    </div>

    <script>
      // iOS Safari 增强版返回键拦截方案
      ;(function () {
        // 设备检测
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
        const isIOSSafari = isIOS && isSafari
        
        // 状态变量
        let userActivated = false
        let historyLength = 0
        let hashChangeBlocked = false
        let interceptActive = false
        let originalHash = location.hash

        console.log("设备检测:", { isIOS, isSafari, isIOSSafari })
        
        // 更新页面显示信息
        function updatePageInfo() {
          const deviceInfo = document.getElementById('deviceInfo')
          const interceptStatus = document.getElementById('interceptStatus')
          
          if (deviceInfo) {
            deviceInfo.textContent = `iOS: ${isIOS}, Safari: ${isSafari}, iOS Safari: ${isIOSSafari}`
          }
          
          if (interceptStatus) {
            interceptStatus.textContent = `拦截激活: ${interceptActive}, 用户激活: ${userActivated}`
          }
        }

        // 用户激活检测
        function markUserActivated() {
          if (!userActivated) {
            userActivated = true
            console.log("用户已激活")
            updatePageInfo()
            
            // 非iOS Safari使用history API
            if (!isIOSSafari) {
              for (let i = 0; i < 30; i++) {
                try {
                  history.pushState({ index: ++historyLength }, null, location.href)
                } catch (e) {
                  break
                }
              }
            }
          }
        }

        // iOS Safari 专用拦截方案
        function setupIOSSafariProtection() {
          console.log("设置 iOS Safari 专用保护")
          interceptActive = true
          updatePageInfo()

          // 方案1: Hash 拦截
          if (!location.hash || location.hash === '#') {
            location.hash = "no-back-ios"
          }

          // 监听 hashchange 事件
          window.addEventListener("hashchange", function(e) {
            console.log("Hash 变化:", e.oldURL, "->", e.newURL)
            
            if (!hashChangeBlocked) {
              hashChangeBlocked = true
              console.log("拦截 hash 变化")
              
              // 阻止默认行为
              e.preventDefault()
              e.stopPropagation()
              
              // 恢复 hash
              setTimeout(() => {
                if (location.hash !== "no-back-ios") {
                  location.hash = "no-back-ios"
                }
                hashChangeBlocked = false
              }, 10)
              
              return false
            }
          }, { capture: true })

          // 方案2: pagehide 事件拦截（iOS 特有）
          window.addEventListener("pagehide", function(e) {
            console.log("页面隐藏事件:", e)
            
            // 阻止页面缓存
            if (e.persisted) {
              e.preventDefault()
              e.stopPropagation()
              return false
            }
          }, { capture: true })

          // 方案3: beforeunload 事件
          window.addEventListener("beforeunload", function(e) {
            console.log("页面卸载事件")
            e.preventDefault()
            e.returnValue = "确定要离开此页面吗？"
            return "确定要离开此页面吗？"
          })

          // 方案4: unload 事件
          window.addEventListener("unload", function(e) {
            console.log("Unload 事件")
            e.preventDefault()
            return false
          }, { capture: true })

          // 方案5: 监听 popstate（即使在iOS Safari中也可能有效）
          window.addEventListener("popstate", function(e) {
            console.log("Popstate 事件:", e.state)
            
            // 立即推送新状态
            setTimeout(() => {
              try {
                history.pushState({ index: ++historyLength, ios: true }, null, location.href)
                console.log("推送新的历史状态")
              } catch (err) {
                console.log("推送状态失败:", err)
              }
            }, 1)
            
            return false
          }, { capture: true })

          // 方案6: 使用 history.forward() 
          setInterval(() => {
            if (userActivated) {
              try {
                // 在iOS Safari中，forward()有时比pushState更有效
                history.forward()
              } catch (e) {
                console.log("Forward 失败:", e)
              }
            }
          }, 500)
        }

        // 通用浏览器保护方案
        function setupGeneralProtection() {
          console.log("设置通用保护")
          interceptActive = true
          updatePageInfo()

          // 建立历史栈
          for (let i = 0; i < 100; i++) {
            try {
              history.pushState({ index: ++historyLength }, null, location.href)
            } catch (e) {
              break
            }
          }

          // 监听 popstate
          window.addEventListener("popstate", function(e) {
            console.log("Popstate 事件被拦截")
            
            if (userActivated) {
              history.pushState({ index: ++historyLength }, null, location.href)
            } else {
              setTimeout(() => {
                try {
                  history.pushState({ index: ++historyLength }, null, location.href)
                } catch (err) {
                  console.log("推送状态失败:", err)
                }
              }, 0)
            }
          })
        }

        // 根据浏览器选择方案
        if (isIOSSafari) {
          setupIOSSafariProtection()
        } else {
          setupGeneralProtection()
        }

        // 监听用户交互事件
        const userEvents = [
          "click", "touchstart", "touchend", "touchmove",
          "mousedown", "mouseup", "mousemove",
          "keydown", "keyup", "scroll", "focus", "blur"
        ]
        
        userEvents.forEach(eventType => {
          document.addEventListener(eventType, markUserActivated, {
            once: false,
            passive: true,
            capture: true
          })
        })

        // 阻止 Backspace 键导航
        document.addEventListener("keydown", function(e) {
          if (e.key === "Backspace" || e.keyCode === 8) {
            if (!["INPUT", "TEXTAREA"].includes(e.target.tagName)) {
              e.preventDefault()
              e.stopPropagation()
              markUserActivated()
              console.log("阻止 Backspace 导航")
              return false
            }
          }
        }, { capture: true })

        // 页面焦点事件
        window.addEventListener("focus", function() {
          console.log("页面获得焦点")
          if (userActivated && !isIOSSafari) {
            for (let i = 0; i < 20; i++) {
              try {
                history.pushState({ index: ++historyLength }, null, location.href)
              } catch (e) {
                break
              }
            }
          }
        })

        // 页面可见性变化
        document.addEventListener("visibilitychange", function() {
          console.log("页面可见性变化:", document.hidden)
          if (!document.hidden && userActivated && !isIOSSafari) {
            for (let i = 0; i < 20; i++) {
              try {
                history.pushState({ index: ++historyLength }, null, location.href)
              } catch (e) {
                break
              }
            }
          }
        })

        // 定时器维护（非iOS Safari）
        if (!isIOSSafari) {
          setInterval(function() {
            if (userActivated) {
              try {
                history.pushState({ index: ++historyLength }, null, location.href)
              } catch (e) {
                // 静默失败
              }
            }
          }, 1000)
        }

        // 页面加载完成后更新信息
        window.addEventListener("load", updatePageInfo)
        document.addEventListener("DOMContentLoaded", updatePageInfo)
      })()

      const app = new Vue({
        el: "#app",
        mounted() {
          console.log("Vue 应用已挂载")
        },
        methods: {
          testInterception() {
            alert("如果你能看到这个弹窗，说明页面仍然活跃。现在尝试点击浏览器的返回按钮测试拦截效果。")
          }
        }
      })
    </script>
  </body>
</html>
